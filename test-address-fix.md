# 地址回显修复测试

## 问题描述
兑礼记录页面点击查看地址时，地址信息未能正确回显。

## 修复内容

### 1. 父页面修改 (pages/index/index.js)
- 在 `handleOpenAddress` 方法中添加了地址信息保存逻辑
- 将从 record-pop 传递过来的地址信息保存到 store 中

### 2. Store修改 (store/store.js)
- 添加了 `currentAddressInfo` 字段用于存储当前选中的地址信息
- 添加了 `setCurrentAddressInfo` 方法用于设置地址信息

### 3. Address-pop组件修改 (components/address-pop/address-pop.js)
- 添加了 `attached` 生命周期方法
- 添加了 `loadAddressInfo` 方法用于加载和回显地址信息
- 支持多种地址字段格式的解析
- 在关闭弹窗时清除地址信息

## 测试步骤

1. 启动项目
2. 进入兑礼记录页面
3. 点击任意一条已完成的记录的"查看地址"按钮
4. 验证地址弹窗中是否正确显示了保存的地址信息：
   - 姓名
   - 手机号
   - 省市区
   - 详细地址

## 预期结果
- 地址信息应该正确回显在对应的输入框中
- 如果是已完成的地址，输入框应该是禁用状态
- 关闭弹窗后，地址信息应该被清除，不影响下次使用

## 技术细节
- 使用 store 作为中介传递地址信息
- 支持多种地址字段格式（region数组、字符串、或分离的province/city/county字段）
- 在组件挂载时自动加载地址信息
- 在组件关闭时自动清理地址信息

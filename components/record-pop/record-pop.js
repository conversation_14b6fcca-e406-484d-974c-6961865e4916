// /Users/<USER>/code/dy-test/kabritaNewUserGift/components/confirm-pop/confirm-pop.js
import dayjs from 'dayjs';
import { getUserReceiveRecord } from '../../utils/constants/api';
import { showToast } from '../../utils/utils';
import create from 'mini-stores';
import store from '../../store/store';
const stores = {
  $store: store, // axml视图上使用$store.xxx即对应store.data.xxx的值。
};
create.Component(stores, {
  data: {
    recordList: [],
    addressPop: false,
    recordPop: false,
    addressInfo: {},
  },
  properties: {},
  async attached() {
    tt.showLoading({
      title: '加载中...',
      success: (res) => {},
      fail: (res) => {},
    });
    try {
      const res = await getUserReceiveRecord();
      // res.data.forEach((item) => {
      //   item.createTime = dayjs(item.createTime).format('YYYY-MM-DD');
      // });
      // store.data.hasFinished = res.data[0].hasFinished;
      store.data.recordList = res.data;
      store.update();
      this.setData({
        recordList: res.data,
      });
      tt.hideLoading();
    } catch (error) {
      console.error(error);
      tt.hideLoading();
      showToast(error.message);
    }
  },
  methods: {
    onClose() {
      console.log(11);
      this.triggerEvent('close');
    },
    handleOpenAddress(e) {
      console.log(5555);
      console.log('1212',e);

      const item = e.currentTarget.dataset.item;
      console.log('点击的item数据:', item);
      store.data.hasFinished = item.hasFinished;
      store.update();
      // 通过事件通信告诉父页面打开地址弹窗
      this.triggerEvent('openAddress', {
        addressInfo: item,
      });
      console.log(6666);
    },
  },
});
